import jieba
import jieba.posseg as pseg
import jieba.analyse
from collections import Counter, defaultdict
import random
import re
import math
from typing import List, Dict, Tuple, Set
import numpy as np

class AdvancedChineseKeywordExtractor:
    def __init__(self):
        """初始化高级中文关键词提取器"""
        # 设置jieba
        jieba.setLogLevel(jieba.logging.INFO)

        # 核心词性权重 - 更精细的权重分配
        self.pos_weights = {
            # 名词类 - 高权重
            'n': 1.0, 'nr': 0.9, 'ns': 1.1, 'nt': 1.2, 'nw': 0.8, 'nz': 1.0,
            # 动词类 - 中高权重
            'v': 0.8, 'vd': 0.7, 'vn': 0.9, 'vshi': 0.6, 'vyou': 0.6,
            # 形容词类 - 中权重
            'a': 0.7, 'ad': 0.6, 'an': 0.8, 'ag': 0.7,
            # 特殊词类 - 高权重
            'i': 1.3,  # 成语
            'l': 1.2,  # 习用语
            'j': 1.1,  # 简称略语
            # 其他
            'eng': 0.9,  # 英文
        }

        # 停用词性
        self.stop_pos = {
            'x', 'w', 'p', 'c', 'u', 'd', 'r', 'm', 'q', 't', 'f', 'b'
        }

        # 扩展停用词集合
        self.stop_words = {
            # 基础停用词
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个',
            '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好',
            '自己', '这', '那', '什么', '还', '可以', '这个', '那个', '但是', '因为', '所以',
            '如果', '虽然', '然后', '或者', '以及', '并且', '而且', '不过', '除了',
            '关于', '对于', '由于', '根据', '通过', '为了', '按照', '依据', '基于',
            # 业务相关停用词
            '订单编号', '商品详情页', '客服', '用户', '系统', '平台', '网站', '页面',
            '功能', '操作', '使用', '点击', '选择', '输入', '确认', '取消', '返回',
            # 时间相关
            '今天', '昨天', '明天', '现在', '刚才', '马上', '立即', '稍后',
            # 程度副词
            '非常', '特别', '比较', '相当', '十分', '极其', '更加', '最',
            # 语气词
            '呢', '吧', '啊', '哦', '嗯', '哈', '呀', '吗'
        }

        # 标点符号
        self.punctuation = set('，。！？；：""''（）【】《》〈〉「」『』…—–·、〔〕〖〗,.!?;:"\'"()[]{}/<>-_|&%$@#*+=~`^')

        # 领域词典权重
        self.domain_patterns = {
            r'[\u4e00-\u9fff]{2,}系统': 1.2,
            r'[\u4e00-\u9fff]{2,}平台': 1.2,
            r'[\u4e00-\u9fff]{2,}服务': 1.1,
            r'[\u4e00-\u9fff]{2,}管理': 1.1,
            r'[\u4e00-\u9fff]{2,}功能': 1.0,
        }

    def extract_keywords(self, text: str, top_k: int = 20, min_chars: int = 2) -> List[Dict]:
        """
        提取关键词的主要接口

        Args:
            text: 输入文本
            top_k: 返回前k个关键词
            min_chars: 最小字符数

        Returns:
            List[Dict]: 关键词列表，每个包含keyword, score, pos等信息
        """
        if not text or len(text.strip()) < min_chars:
            return []

        # 预处理文本
        cleaned_text = self._preprocess_text(text)

        # 多策略提取关键词
        candidates = self._extract_keyword_candidates(cleaned_text)

        # 计算关键词分数
        scored_keywords = self._calculate_keyword_scores(candidates, cleaned_text)

        # 排序并返回top_k
        sorted_keywords = sorted(scored_keywords, key=lambda x: x['score'], reverse=True)

        return sorted_keywords[:top_k]

    def _preprocess_text(self, text: str) -> str:
        """预处理文本"""
        # 去除多余空白
        text = re.sub(r'\s+', ' ', text.strip())

        # 去除特殊字符但保留中文标点
        text = re.sub(r'[^\u4e00-\u9fff\w\s，。！？；：""''（）【】《》]', ' ', text)

        return text

    def _extract_keyword_candidates(self, text: str) -> List[Dict]:
        """提取关键词候选"""
        candidates = []

        # 分词和词性标注
        words_with_pos = list(pseg.cut(text))

        # 策略1: 单词级别关键词
        single_word_candidates = self._extract_single_words(words_with_pos)
        candidates.extend(single_word_candidates)

        # 策略2: 短语级别关键词
        phrase_candidates = self._extract_phrases(words_with_pos)
        candidates.extend(phrase_candidates)

        # 策略3: 使用jieba的TF-IDF和TextRank
        tfidf_keywords = self._extract_with_jieba_algorithms(text)
        candidates.extend(tfidf_keywords)

        return candidates

    def _extract_single_words(self, words_with_pos: List[Tuple[str, str]]) -> List[Dict]:
        """提取单词级别的关键词候选"""
        candidates = []

        for word, pos in words_with_pos:
            # 过滤条件
            if (len(word) < 2 or
                word in self.stop_words or
                pos in self.stop_pos or
                any(p in word for p in self.punctuation) or
                self._is_english_word(word)):  # 排除英文单词
                continue

            # 计算基础权重
            weight = self.pos_weights.get(pos, 0.5)

            # 领域词典加权
            for pattern, bonus in self.domain_patterns.items():
                if re.search(pattern, word):
                    weight *= bonus
                    break

            candidates.append({
                'keyword': word,
                'type': 'single_word',
                'pos': pos,
                'base_weight': weight,
                'length': len(word)
            })

        return candidates

    def _extract_phrases(self, words_with_pos: List[Tuple[str, str]]) -> List[Dict]:
        """提取短语级别的关键词候选"""
        candidates = []

        # 提取2-4词的短语
        for length in range(2, 5):
            for i in range(len(words_with_pos) - length + 1):
                window = words_with_pos[i:i + length]

                if self._is_valid_phrase(window):
                    phrase = ''.join([word for word, pos in window])

                    # 排除包含英文的短语
                    if self._contains_english(phrase):
                        continue

                    # 计算短语权重
                    avg_weight = sum(self.pos_weights.get(pos, 0.5) for _, pos in window) / length

                    candidates.append({
                        'keyword': phrase,
                        'type': 'phrase',
                        'pos': [pos for _, pos in window],
                        'base_weight': avg_weight,
                        'length': len(phrase),
                        'word_count': length
                    })

        return candidates

    def _extract_with_jieba_algorithms(self, text: str) -> List[Dict]:
        """使用jieba内置算法提取关键词"""
        candidates = []

        try:
            # TF-IDF
            tfidf_keywords = jieba.analyse.extract_tags(text, topK=20, withWeight=True)
            for word, weight in tfidf_keywords:
                if (len(word) >= 2 and
                    word not in self.stop_words and
                    not self._is_english_word(word)):  # 排除英文单词
                    candidates.append({
                        'keyword': word,
                        'type': 'tfidf',
                        'pos': 'unknown',
                        'base_weight': weight,
                        'length': len(word)
                    })

            # TextRank
            textrank_keywords = jieba.analyse.textrank(text, topK=20, withWeight=True)
            for word, weight in textrank_keywords:
                if (len(word) >= 2 and
                    word not in self.stop_words and
                    not self._is_english_word(word)):  # 排除英文单词
                    candidates.append({
                        'keyword': word,
                        'type': 'textrank',
                        'pos': 'unknown',
                        'base_weight': weight,
                        'length': len(word)
                    })
        except:
            pass

        return candidates

    def _is_english_word(self, word: str) -> bool:
        """检查是否为纯英文单词"""
        return bool(re.match(r'^[A-Za-z]+$', word))

    def _contains_english(self, text: str) -> bool:
        """检查文本是否包含英文字符"""
        return bool(re.search(r'[A-Za-z]', text))

    def _is_valid_phrase(self, words_with_pos: List[Tuple[str, str]]) -> bool:
        """判断短语是否有效"""
        if not words_with_pos:
            return False

        words = [word for word, pos in words_with_pos]
        poses = [pos for word, pos in words_with_pos]

        # 不能全是停用词性
        if all(pos in self.stop_pos for pos in poses):
            return False

        # 必须包含有意义的词性
        if not any(pos in self.pos_weights for pos in poses):
            return False

        # 不能以标点开头或结尾
        phrase = ''.join(words)
        if any(p in phrase[0] + phrase[-1] for p in self.punctuation):
            return False

        # 不能主要由停用词组成
        stop_count = sum(1 for word in words if word in self.stop_words)
        if stop_count > len(words) * 0.6:
            return False

        return True

    def _calculate_keyword_scores(self, candidates: List[Dict], text: str) -> List[Dict]:
        """计算关键词分数"""
        # 统计词频
        word_freq = Counter()
        for candidate in candidates:
            word_freq[candidate['keyword']] += 1

        # 计算文档长度
        doc_length = len(text)

        scored_keywords = []
        seen_keywords = set()

        for candidate in candidates:
            keyword = candidate['keyword']

            # 去重
            if keyword in seen_keywords:
                continue
            seen_keywords.add(keyword)

            # 基础分数
            base_score = candidate['base_weight']

            # 频率分数 (TF)
            tf_score = word_freq[keyword] / doc_length

            # 长度分数 (偏好适中长度)
            length_score = self._calculate_length_score(len(keyword))

            # 位置分数 (开头的词权重更高)
            position_score = self._calculate_position_score(keyword, text)

            # 类型分数
            type_score = self._get_type_score(candidate['type'])

            # 综合分数
            final_score = (base_score * 0.3 +
                          tf_score * 0.2 +
                          length_score * 0.2 +
                          position_score * 0.15 +
                          type_score * 0.15)

            scored_keywords.append({
                'keyword': keyword,
                'score': final_score,
                'type': candidate['type'],
                'pos': candidate.get('pos', 'unknown'),
                'frequency': word_freq[keyword],
                'length': len(keyword)
            })

        return scored_keywords

    def _calculate_length_score(self, length: int) -> float:
        """计算长度分数，偏好2-6字的词"""
        if 2 <= length <= 4:
            return 1.0
        elif 5 <= length <= 6:
            return 0.8
        elif length == 7:
            return 0.6
        else:
            return 0.4

    def _calculate_position_score(self, keyword: str, text: str) -> float:
        """计算位置分数"""
        first_pos = text.find(keyword)
        if first_pos == -1:
            return 0.5

        # 越靠前权重越高
        position_ratio = first_pos / len(text)
        return max(0.3, 1.0 - position_ratio)

    def _get_type_score(self, keyword_type: str) -> float:
        """获取类型分数"""
        type_scores = {
            'single_word': 0.8,
            'phrase': 1.0,
            'tfidf': 0.9,
            'textrank': 0.9
        }
        return type_scores.get(keyword_type, 0.5)


def extract_keywords_with_advanced_algorithm(texts: List[str],
                                           sample_rate: float = 0.02,
                                           min_freq: int = 2,
                                           top_k: int = 1000,
                                           min_chars: int = 2) -> Counter:
    """
    使用高级算法提取关键词

    Args:
        texts: 文本列表
        sample_rate: 采样率
        min_freq: 最小频次
        top_k: 保留的候选数量
        min_chars: 最小字符数

    Returns:
        Counter: 关键词频次统计
    """
    extractor = AdvancedChineseKeywordExtractor()

    # 采样
    sample_size = max(int(len(texts) * sample_rate), 200)
    sample_texts = random.sample(texts, min(sample_size, len(texts)))

    print(f"从 {len(texts)} 个文本中采样 {len(sample_texts)} 个进行分析")

    # 提取关键词候选
    keyword_counter = Counter()
    processed = 0

    for text in sample_texts:
        processed += 1
        if processed % 100 == 0:
            print(f"处理进度: {processed}/{len(sample_texts)}")

        keywords = extractor.extract_keywords(text, top_k=50, min_chars=min_chars)

        # 统计关键词频次
        for kw in keywords:
            keyword_counter[kw['keyword']] += 1

    print(f"提取到 {len(keyword_counter)} 个不同的关键词")

    # 获取高频候选
    candidates = [kw for kw, count in keyword_counter.most_common(top_k)
                 if count >= min_freq]

    print(f"筛选出 {len(candidates)} 个候选关键词进行全量验证")

    # 在全量数据中验证
    final_results = Counter()
    chunk_size = 3000

    for i, candidate in enumerate(candidates):
        if i % 100 == 0:
            print(f"验证进度: {i}/{len(candidates)}")

        total_count = 0
        for j in range(0, len(texts), chunk_size):
            chunk = texts[j:j+chunk_size]
            chunk_count = sum(1 for text in chunk if candidate in text)
            total_count += chunk_count

            # 早停优化
            if total_count >= min_freq * 50:
                break

        if total_count >= min_freq:
            final_results[candidate] = total_count

    print(f"最终找到 {len(final_results)} 个有意义的关键词")

    return final_results


# 保持向后兼容的函数名
def jieba_optimized_common_substring(texts, sample_rate=0.02, min_freq=2,
                                   min_words=2, max_words=6, min_chars=2, top_k=1000):
    """
    基于jieba的中文公共有意义子串提取 (重构版本)

    Args:
        texts: 文本列表
        sample_rate: 采样率
        min_freq: 最小频次
        min_words: 最小词数 (已废弃，保持兼容性)
        max_words: 最大词数 (已废弃，保持兼容性)
        min_chars: 最小字符数
        top_k: 保留的候选数量
    """
    return extract_keywords_with_advanced_algorithm(
        texts=texts,
        sample_rate=sample_rate,
        min_freq=min_freq,
        top_k=top_k,
        min_chars=min_chars
    )


def extract_keywords_from_human_data(human_data,
                                   text_field='解释',
                                   sample_rate=0.02,
                                   min_freq=2,
                                   min_words=2,  # 保持兼容性
                                   max_words=5,  # 保持兼容性
                                   min_chars=3,
                                   top_k=1000):
    """
    从human_data中提取关键词，并保存到对应索引下 (重构版本)

    Args:
        human_data: 输入的人类数据列表
        text_field: 要提取关键词的字段名
        sample_rate: 采样率
        min_freq: 最小频次
        min_words: 最小词数 (已废弃，保持兼容性)
        max_words: 最大词数 (已废弃，保持兼容性)
        min_chars: 最小字符数
        top_k: 保留的候选数量

    Returns:
        tuple: (enhanced_human_data, word_frequency_counter)
    """

    print(f"开始处理 {len(human_data)} 条数据...")

    # 提取文本列表
    texts = []
    valid_indices = []

    for i, item in enumerate(human_data):
        try:
            text = item['message']['request'][text_field]
            if text and isinstance(text, str) and len(text.strip()) > 0:
                texts.append(text.strip())
                valid_indices.append(i)
        except (KeyError, TypeError) as e:
            print(f"索引 {i} 处理失败: {e}")
            continue

    print(f"成功提取 {len(texts)} 条有效文本")

    if not texts:
        print("没有找到有效的文本数据")
        return human_data, Counter()

    # 使用新的算法提取关键词
    word_frequency = extract_keywords_with_advanced_algorithm(
        texts=texts,
        sample_rate=sample_rate,
        min_freq=min_freq,
        top_k=top_k,
        min_chars=min_chars
    )

    print(f"提取到 {len(word_frequency)} 个高频关键词")

    # 为每个数据项添加关键词
    enhanced_human_data = []
    extractor = AdvancedChineseKeywordExtractor()

    for i, item in enumerate(human_data):
        enhanced_item = item.copy()
        keywords = []

        try:
            text = item['message']['response'][text_field]

            if text and isinstance(text, str):
                # 提取当前文本的关键词
                all_keywords = extractor.extract_keywords(text, top_k=50, min_chars=min_chars)

                # 只保留在高频关键词中的部分
                for kw in all_keywords:
                    if kw['keyword'] in word_frequency:
                        keywords.append({
                            'keyword': kw['keyword'],
                            'frequency': word_frequency[kw['keyword']],
                            'score': kw['score']
                        })

                # 按频次排序
                keywords.sort(key=lambda x: x['frequency'], reverse=True)

        except (KeyError, TypeError) as e:
            print(f"为索引 {i} 添加关键词时出错: {e}")

        enhanced_item['keywords'] = keywords
        enhanced_human_data.append(enhanced_item)

    print(f"完成关键词提取，平均每条数据有 {sum(len(item['keywords']) for item in enhanced_human_data) / len(enhanced_human_data):.1f} 个关键词")

    return enhanced_human_data, word_frequency

def extract_keywords_from_pre_user_data(human_data,
                                   text_field='解释',  # 保持兼容性
                                   sample_rate=0.02,
                                   min_freq=2,
                                   min_words=2,  # 保持兼容性
                                   max_words=5,  # 保持兼容性
                                   min_chars=3,
                                   top_k=1000):
    """
    从human_data中提取关键词，并保存到对应索引下 (重构版本)

    Args:
        human_data: 输入的人类数据列表
        text_field: 要提取关键词的字段名 (已废弃，保持兼容性)
        sample_rate: 采样率
        min_freq: 最小频次
        min_words: 最小词数 (已废弃，保持兼容性)
        max_words: 最大词数 (已废弃，保持兼容性)
        min_chars: 最小字符数
        top_k: 保留的候选数量

    Returns:
        tuple: (enhanced_human_data, word_frequency_counter)
    """

    print(f"开始处理 {len(human_data)} 条数据...")

    # 提取文本列表
    texts = []
    valid_indices = []

    for i, item in enumerate(human_data):
        try:
            text = item['message']['user_request']
            if text and isinstance(text, str) and len(text.strip()) > 0:
                texts.append(text.strip())
                valid_indices.append(i)
        except (KeyError, TypeError) as e:
            print(f"索引 {i} 处理失败: {e}")
            continue

    print(f"成功提取 {len(texts)} 条有效文本")

    if not texts:
        print("没有找到有效的文本数据")
        return human_data, Counter()

    # 使用新的算法提取关键词
    word_frequency = extract_keywords_with_advanced_algorithm(
        texts=texts,
        sample_rate=sample_rate,
        min_freq=min_freq,
        top_k=top_k,
        min_chars=min_chars
    )

    print(f"提取到 {len(word_frequency)} 个高频关键词")

    # 为每个数据项添加关键词
    enhanced_human_data = []
    extractor = AdvancedChineseKeywordExtractor()

    for i, item in enumerate(human_data):
        enhanced_item = item.copy()
        keywords = []

        try:
            text = item['message']['user_request']

            if text and isinstance(text, str):
                # 提取当前文本的关键词
                all_keywords = extractor.extract_keywords(text, top_k=50, min_chars=min_chars)

                # 只保留在高频关键词中的部分
                for kw in all_keywords:
                    if kw['keyword'] in word_frequency:
                        keywords.append({
                            'keyword': kw['keyword'],
                            'frequency': word_frequency[kw['keyword']],
                            'score': kw['score']
                        })

                # 按频次排序
                keywords.sort(key=lambda x: x['frequency'], reverse=True)

        except (KeyError, TypeError) as e:
            print(f"为索引 {i} 添加关键词时出错: {e}")

        enhanced_item['keywords'] = keywords
        enhanced_human_data.append(enhanced_item)

    print(f"完成关键词提取，平均每条数据有 {sum(len(item['keywords']) for item in enhanced_human_data) / len(enhanced_human_data):.1f} 个关键词")

    return enhanced_human_data, word_frequency


# 测试示例
if __name__ == "__main__":
    # 测试新的关键词提取功能
    sample_human_data = [
        {
            'message': {
                'request': {
                    '解释': '因为知识库未提及相关内容，所以不回复。这是一个重要的问题。'
                },
                'response': {
                    '解释': '因为知识库未提及相关内容，所以不回复。这是一个重要的问题。'
                }
            }
        },
        {
            'message': {
                'request': {
                    '解释': '知识库未提及相关内容，因此无法回答。请提供更多信息。'
                },
                'response': {
                    '解释': '知识库未提及相关内容，因此无法回答。请提供更多信息。'
                }
            }
        },
        {
            'message': {
                'request': {
                    '解释': '未提及相关内容的问题比较复杂，需要进一步分析。'
                },
                'response': {
                    '解释': '未提及相关内容的问题比较复杂，需要进一步分析。'
                }
            }
        }
    ]

    print("=== 测试新的关键词提取算法 ===")

    # 测试单个文本的关键词提取
    extractor = AdvancedChineseKeywordExtractor()
    test_text = "因为知识库未提及相关内容，所以不回复。这是一个重要的问题，需要进一步分析和处理。"
    keywords = extractor.extract_keywords(test_text, top_k=10, min_chars=2)

    print(f"\n原文: {test_text}")
    print("提取的关键词:")
    for kw in keywords:
        print(f"  {kw['keyword']} (分数: {kw['score']:.3f}, 类型: {kw['type']})")

    # 测试批量数据处理
    print("\n=== 测试批量数据处理 ===")
    enhanced_data, word_freq = extract_keywords_from_human_data(
        human_data=sample_human_data,
        text_field='解释',
        sample_rate=1.0,  # 使用全量数据
        min_freq=1,  # 降低频次要求以便测试
        min_chars=2
    )

    print("\n词频统计结果:")
    for word, freq in word_freq.most_common(10):
        print(f"  '{word}': {freq}次")

    print("\n每条数据的关键词:")
    for i, item in enumerate(enhanced_data):
        print(f"\n数据 {i}:")
        print(f"  原文: {item['message']['response']['解释']}")
        keywords_list = [f"{kw['keyword']}({kw['frequency']})" for kw in item['keywords'][:5]]
        print(f"  关键词: {keywords_list}")

    print("\n=== 测试完成 ===")