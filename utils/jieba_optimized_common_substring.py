import jieba
import jieba.posseg as pseg
from collections import Counter, defaultdict
import random
import re

class JiebaChineseExtractor:
    def __init__(self):
        """初始化jieba分词器"""
        # 设置jieba为精确模式
        jieba.setLogLevel(jieba.logging.INFO)
        
        # 可以添加自定义词典
        # jieba.load_userdict("user_dict.txt")
        
        # 定义有意义的词性标签
        self.meaningful_pos = {
            'n', 'nr', 'ns', 'nt', 'nw', 'nz',  # 名词类
            'v', 'vd', 'vn', 'vshi', 'vyou',    # 动词类
            'a', 'ad', 'an', 'ag',              # 形容词类
            'i',                                # 成语
            'l',                                # 习用语
            'j',                                # 简称略语
        }
        
        # 定义停用词性（需要过滤的）
        self.stop_pos = {
            'x', 'w', 'PER', 'LOC', 'ORG',     # 标点、人名、地名等
            'p', 'c', 'u', 'd', 'r',           # 介词、连词、助词、副词、代词
            'm', 'q', 't',                      # 数词、量词、时间
        }
        
        # 定义中文标点符号集合
        self.chinese_punctuation = {
            '，', '。', '！', '？', '；', '：', '"', '"', ''', ''', 
            '（', '）', '【', '】', '《', '》', '〈', '〉', '「', '」',
            '『', '』', '…', '—', '–', '·', '、', '〔', '〕', '〖', '〗'
        }
        
        # 定义英文标点符号集合
        self.english_punctuation = {
            ',', '.', '!', '?', ';', ':', '"', "'", '(', ')', '[', ']',
            '{', '}', '<', '>', '-', '_', '/', '\\', '|', '&', '%', '$',
            '@', '#', '*', '+', '=', '~', '`', '^'
        }
        
        # 合并所有标点符号
        self.all_punctuation = self.chinese_punctuation | self.english_punctuation
        
        # 常见的停用词
        self.stop_words = {
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', 
            '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', 
            '自己', '这', '那', '什么', '还', '可以', '这个', '那个', '但是', '因为', '所以',
            '如果', '虽然', '然后', '或者', '以及', '并且', '而且', '但是', '不过', '除了',
            '关于', '对于', '由于', '根据', '通过', '为了', '按照', '依据', '基于'
            ,'订单编号',"商品详情页"
        }
    
    def extract_meaningful_segments(self, text, min_words=2, max_words=8, min_chars=2):
        """
        从文本中提取有意义的中文片段
        
        Args:
            text: 输入文本
            min_words: 最小词数
            max_words: 最大词数
            min_chars: 最小字符数
        
        Returns:
            list: 有意义的片段列表
        """
        if not text or len(text.strip()) < min_chars:
            return []
        
        meaningful_segments = []
        
        try:
            # 词性标注
            words_with_pos = list(pseg.cut(text))
            
            if len(words_with_pos) < min_words:
                return []
            
            # 策略1: 提取连续的有意义词汇组合（改进版，避免重复）
            coherent_segments = self._extract_coherent_phrases_improved(words_with_pos, min_words, max_words)
            meaningful_segments.extend(coherent_segments)
            
            # 策略2: 提取名词短语
            noun_phrases = self._extract_noun_phrases(words_with_pos)
            meaningful_segments.extend(noun_phrases)
            
            # 策略3: 提取动词短语
            verb_phrases = self._extract_verb_phrases(words_with_pos)
            meaningful_segments.extend(verb_phrases)
            
            # 策略4: 提取成语和习用语
            idioms = self._extract_idioms_and_phrases(words_with_pos, text)
            meaningful_segments.extend(idioms)
            
            # 策略5: 提取专业术语和固定搭配
            terms = self._extract_technical_terms(words_with_pos)
            meaningful_segments.extend(terms)
            
        except Exception as e:
            print(f"处理文本时出错: {e}")
            return []
        
        # 去重、过滤和后处理（改进版，去除包含关系）
        cleaned_segments = self._clean_and_filter_improved(meaningful_segments, min_chars)
        
        return cleaned_segments
    
    def _has_punctuation_inside(self, text):
        """
        检查文本中间是否包含标点符号（不包括首尾）
        
        Args:
            text: 要检查的文本
        
        Returns:
            bool: 如果中间包含标点符号返回True，否则返回False
        """
        if len(text) <= 2:
            return False
        
        # 检查除了首尾字符之外的中间部分
        middle_part = text[1:-1]
        
        # 检查中间部分是否包含任何标点符号
        return any(char in self.all_punctuation for char in middle_part)
    
    def _extract_coherent_phrases_improved(self, words_with_pos, min_words, max_words):
        """
        改进的连贯短语提取，避免重复子串
        使用最长匹配策略，优先保留更长的有意义短语
        """
        coherent_phrases = []
        used_positions = set()  # 记录已使用的位置，避免重复
        
        # 从最长的短语开始匹配
        for length in range(min(max_words, len(words_with_pos)), min_words - 1, -1):
            for i in range(len(words_with_pos) - length + 1):
                # 检查这个位置范围是否已被使用
                if any(pos in used_positions for pos in range(i, i + length)):
                    continue
                
                window = words_with_pos[i:i + length]
                
                if self._is_meaningful_phrase(window):
                    phrase = ''.join([word for word, pos in window])
                    
                    # 检查是否为停用词组合
                    if not self._is_stop_phrase(phrase):
                        coherent_phrases.append(phrase)
                        # 标记这些位置已被使用
                        used_positions.update(range(i, i + length))
        
        return coherent_phrases
    
    def _is_stop_phrase(self, phrase):
        """检查是否为停用短语"""
        # 如果整个短语都是停用词，则过滤
        if phrase in self.stop_words:
            return True
        
        # 如果短语主要由停用词组成（超过80%），则过滤
        words = list(jieba.cut(phrase))
        if len(words) > 1:
            stop_count = sum(1 for word in words if word in self.stop_words)
            if stop_count / len(words) > 0.8:
                return True
        
        return False
    
    def _remove_substring_duplicates(self, segments):
        """
        去除包含关系的重复片段
        保留更长的片段，去除被包含的短片段
        """
        if not segments:
            return []
        
        # 按长度排序，长的在前
        sorted_segments = sorted(set(segments), key=len, reverse=True)
        result = []
        
        for current in sorted_segments:
            # 检查当前片段是否被已保留的片段包含
            is_contained = False
            for kept in result:
                if current in kept and current != kept:
                    is_contained = True
                    break
            
            if not is_contained:
                result.append(current)
        
        return result
    
    def _extract_noun_phrases(self, words_with_pos):
        """提取名词短语"""
        noun_phrases = []
        current_phrase = []
        
        # 名词相关词性
        noun_pos = {'n', 'nr', 'ns', 'nt', 'nw', 'nz', 'a', 'an', 'ag'}
        
        for word, pos in words_with_pos:
            if pos in noun_pos and word not in self.stop_words:
                current_phrase.append(word)
            else:
                if len(current_phrase) >= 2:
                    phrase = ''.join(current_phrase)
                    if len(phrase) >= 2:
                        noun_phrases.append(phrase)
                current_phrase = []
        
        # 处理最后一个短语
        if len(current_phrase) >= 2:
            phrase = ''.join(current_phrase)
            if len(phrase) >= 2:
                noun_phrases.append(phrase)
        
        return noun_phrases
    
    def _extract_verb_phrases(self, words_with_pos):
        """提取动词短语"""
        verb_phrases = []
        
        for i, (word, pos) in enumerate(words_with_pos):
            if pos.startswith('v') and word not in self.stop_words:  # 动词
                phrase_parts = [word]
                
                # 向后寻找宾语、补语
                for j in range(i + 1, min(i + 4, len(words_with_pos))):
                    next_word, next_pos = words_with_pos[j]
                    
                    # 过滤停用词
                    if next_word in self.stop_words:
                        continue
                    
                    # 如果是名词、形容词或者动词补语
                    if next_pos in {'n', 'nr', 'ns', 'nt', 'nw', 'nz', 'a', 'an', 'vn', 'd', 'ad'}:
                        phrase_parts.append(next_word)
                    elif next_pos in {'w', 'x'}:  # 遇到标点停止
                        break
                    elif len(phrase_parts) >= 3:  # 已经足够长了
                        break
                
                if len(phrase_parts) >= 2:
                    phrase = ''.join(phrase_parts)
                    if len(phrase) >= 3:
                        verb_phrases.append(phrase)
        
        return verb_phrases
    
    def _extract_idioms_and_phrases(self, words_with_pos, original_text):
        """提取成语和习用语"""
        idioms = []
        
        # 策略1: jieba识别出的成语和习用语
        for word, pos in words_with_pos:
            if pos in {'i', 'l'} and len(word) >= 2:  # 成语、习用语
                idioms.append(word)
        
        # 策略2: 四字成语模式识别
        four_char_idioms = self._find_four_char_idioms(original_text)
        idioms.extend(four_char_idioms)
        
        # 策略3: 常见搭配模式
        collocations = self._find_common_collocations(words_with_pos)
        idioms.extend(collocations)
        
        return idioms
    
    def _find_four_char_idioms(self, text):
        """查找四字成语"""
        idioms = []
        
        # 四字成语正则模式
        four_char_pattern = r'[\u4e00-\u9fff]{4}'
        matches = re.finditer(four_char_pattern, text)
        
        for match in matches:
            candidate = match.group()
            if self._is_likely_idiom(candidate):
                idioms.append(candidate)
        
        return idioms
    
    def _is_likely_idiom(self, text):
        """判断是否可能是成语"""
        if len(text) != 4:
            return False
        
        # 成语常见模式
        idiom_patterns = [
            r'^一.{2}$', r'^不.{2}$', r'^有.{2}$', r'^无.{2}$',
            r'^.{2}不.{1}$', r'^.{2}而.{1}$', r'^.{2}之.{1}$',
            r'^.{1}然.{2}$', r'^.{1}者.{2}$', r'^.{1}所.{2}$'
        ]
        
        return any(re.match(pattern, text) for pattern in idiom_patterns)
    
    def _find_common_collocations(self, words_with_pos):
        """查找常见搭配"""
        collocations = []
        
        # 定义搭配模式
        patterns = [
            # 动词 + 名词
            (lambda pos1, pos2: pos1.startswith('v') and pos2.startswith('n')),
            # 形容词 + 名词
            (lambda pos1, pos2: pos1.startswith('a') and pos2.startswith('n')),
            # 名词 + 名词（复合名词）
            (lambda pos1, pos2: pos1.startswith('n') and pos2.startswith('n')),
            # 副词 + 动词
            (lambda pos1, pos2: pos1 in {'d', 'ad'} and pos2.startswith('v')),
        ]
        
        for i in range(len(words_with_pos) - 1):
            word1, pos1 = words_with_pos[i]
            word2, pos2 = words_with_pos[i + 1]
            
            # 过滤停用词
            if word1 in self.stop_words or word2 in self.stop_words:
                continue
            
            for pattern in patterns:
                if pattern(pos1, pos2):
                    collocation = word1 + word2
                    if len(collocation) >= 2 and len(word1) >= 1 and len(word2) >= 1:
                        collocations.append(collocation)
        
        return collocations
    
    def _extract_technical_terms(self, words_with_pos):
        """提取专业术语"""
        terms = []
        
        # 查找可能的专业术语模式
        # 英文字母 + 中文（如：AI人工智能）
        for i in range(len(words_with_pos) - 1):
            word1, pos1 = words_with_pos[i]
            word2, pos2 = words_with_pos[i + 1]
            
            # 英文+中文组合
            if re.match(r'^[A-Za-z]+$', word1) and pos2.startswith('n'):
                term = word1 + word2
                terms.append(term)
            
            # 数字+单位+名词组合
            if pos1 == 'm' and pos2.startswith('n'):  # 数词+名词
                term = word1 + word2
                terms.append(term)
        
        return terms
    
    def _is_meaningful_phrase(self, words_with_pos):
        """判断短语是否有意义"""
        if not words_with_pos:
            return False
        
        words = [word for word, pos in words_with_pos]
        poses = [pos for word, pos in words_with_pos]
        
        # 规则1: 不能全是停用词性
        if all(pos in self.stop_pos for pos in poses):
            return False
        
        # 规则2: 必须包含有意义的词性
        if not any(pos in self.meaningful_pos or pos.startswith(('n', 'v', 'a')) for pos in poses):
            return False
        
        # 规则3: 不能以标点开头或结尾
        if poses[0] in {'w', 'x'} or poses[-1] in {'w', 'x'}:
            return False
        
        # 规则4: 不能有太多单字词
        single_chars = sum(1 for word in words if len(word) == 1)
        if single_chars > len(words) * 0.6:
            return False
        
        # 规则5: 总长度检查
        total_length = sum(len(word) for word in words)
        if total_length < 2:
            return False
        
        # 规则6: 不能主要由停用词组成
        phrase = ''.join(words)
        if self._is_stop_phrase(phrase):
            return False
        
        return True
    
    def _clean_and_filter_improved(self, segments, min_chars):
        """改进的清理和过滤片段方法"""
        cleaned = []
        
        for segment in segments:
            # 去除空白字符
            segment = segment.strip()
            
            # 长度过滤
            if len(segment) < min_chars:
                continue
            
            # 去除纯数字、纯英文、纯标点
            if re.match(r'^[\d\s]+$', segment):
                continue
            if re.match(r'^[A-Za-z\s]+$', segment):
                continue
            if re.match(r'^[\W\s]+$', segment):
                continue
            
            # 确保包含中文字符
            if not re.search(r'[\u4e00-\u9fff]', segment):
                continue
            
            # 过滤中间包含标点符号的子串
            if self._has_punctuation_inside(segment):
                continue
            
            # 过滤停用短语
            if self._is_stop_phrase(segment):
                continue
            
            cleaned.append(segment)
        
        # 去除包含关系的重复片段
        deduplicated = self._remove_substring_duplicates(cleaned)
        
        return deduplicated


def jieba_optimized_common_substring(texts, sample_rate=0.02, min_freq=2, 
                                   min_words=2, max_words=6, min_chars=2, top_k=1000):
    """
    基于jieba的中文公共有意义子串提取
    
    Args:
        texts: 文本列表
        sample_rate: 采样率
        min_freq: 最小频次
        min_words: 最小词数
        max_words: 最大词数
        min_chars: 最小字符数
        top_k: 保留的候选数量
    """
    
    extractor = JiebaChineseExtractor()
    
    # 第一步：采样
    sample_size = max(int(len(texts) * sample_rate), 200)
    sample_texts = random.sample(texts, min(sample_size, len(texts)))
    
    print(f"从 {len(texts)} 个文本中采样 {len(sample_texts)} 个进行分析")
    
    # 第二步：提取有意义的片段
    segment_counter = Counter()
    processed = 0
    
    for text in sample_texts:
        processed += 1
        if processed % 100 == 0:
            print(f"处理进度: {processed}/{len(sample_texts)}")
        
        segments = extractor.extract_meaningful_segments(
            text, min_words, max_words, min_chars
        )
        
        # 统计片段频次（去重，每个文本中的片段只计算一次）
        unique_segments = set(segments)
        for segment in unique_segments:
            segment_counter[segment] += 1
    
    print(f"提取到 {len(segment_counter)} 个不同的有意义片段")
    
    # 第三步：获取高频候选
    candidates = [segment for segment, count in segment_counter.most_common(top_k) 
                 if count >= min_freq]
    
    print(f"筛选出 {len(candidates)} 个候选片段进行全量验证")
    
    # 第四步：在全量数据中验证
    final_results = Counter()
    chunk_size = 3000
    
    for i, candidate in enumerate(candidates):
        if i % 100 == 0:
            print(f"验证进度: {i}/{len(candidates)}")
        
        total_count = 0
        for j in range(0, len(texts), chunk_size):
            chunk = texts[j:j+chunk_size]
            chunk_count = sum(1 for text in chunk if candidate in text)
            total_count += chunk_count
            
            # 早停优化
            if total_count >= min_freq * 50:  # 如果已经很高频，可以提前停止
                break
        
        if total_count >= min_freq:
            final_results[candidate] = total_count
    
    print(f"最终找到 {len(final_results)} 个有意义的公共片段")
    
    return final_results


def extract_keywords_from_human_data(human_data, 
                                   text_field='解释',  # 指定要提取的字段
                                   sample_rate=0.02, 
                                   min_freq=2,
                                   min_words=2, 
                                   max_words=5, 
                                   min_chars=3,
                                   top_k=1000):
    """
    从human_data中提取关键词，并保存到对应索引下
    
    Args:
        human_data: 输入的人类数据列表，每个元素应该包含['message']['response']字段
        text_field: 要提取关键词的字段名，默认为'解释'
        sample_rate: 采样率
        min_freq: 最小频次
        min_words: 最小词数
        max_words: 最大词数
        min_chars: 最小字符数
        top_k: 保留的候选数量
    
    Returns:
        tuple: (enhanced_human_data, word_frequency_counter)
            - enhanced_human_data: 增强后的数据，每个元素添加了'keywords'字段
            - word_frequency_counter: 词频统计Counter对象
    """
    
    print(f"开始处理 {len(human_data)} 条数据...")
    
    # 提取文本列表
    texts = []
    valid_indices = []  # 记录有效的索引
    
    for i, item in enumerate(human_data):
        try:
            # 尝试提取指定字段的文本
            text = item['message']['request'][text_field]
            # text = item['reponse_JSON'][text_field]
            if text and isinstance(text, str) and len(text.strip()) > 0:
                texts.append(text.strip())
                valid_indices.append(i)
        except (KeyError, TypeError) as e:
            print(f"索引 {i} 处理失败: {e}")
            continue
    
    print(f"成功提取 {len(texts)} 条有效文本")
    
    if not texts:
        print("没有找到有效的文本数据")
        return human_data, Counter()
    
    # 使用原有函数提取公共关键词
    word_frequency = jieba_optimized_common_substring(
        texts=texts,
        sample_rate=sample_rate,
        min_freq=min_freq,
        min_words=min_words,
        max_words=max_words,
        min_chars=min_chars,
        top_k=top_k
    )
    
    print(f"提取到 {len(word_frequency)} 个高频关键词")
    
    # 为每个数据项添加关键词
    enhanced_human_data = []
    extractor = JiebaChineseExtractor()
    
    for i, item in enumerate(human_data):
        # 复制原始数据
        enhanced_item = item.copy()
        
        # 初始化关键词列表
        keywords = []
        
        try:
            # 获取文本
            text = item['message']['response'][text_field]
            
            if text and isinstance(text, str):
                # 提取当前文本的所有关键词
                all_segments = extractor.extract_meaningful_segments(
                    text, min_words, max_words, min_chars
                )
                
                # 只保留在高频关键词中的部分
                for segment in all_segments:
                    if segment in word_frequency:
                        keywords.append({
                            'keyword': segment,
                            'frequency': word_frequency[segment]
                        })
                
                # 按频次排序
                keywords.sort(key=lambda x: x['frequency'], reverse=True)
        
        except (KeyError, TypeError) as e:
            print(f"为索引 {i} 添加关键词时出错: {e}")
        
        # 添加关键词字段
        enhanced_item['keywords'] = keywords
        enhanced_human_data.append(enhanced_item)
    
    print(f"完成关键词提取，平均每条数据有 {sum(len(item['keywords']) for item in enhanced_human_data) / len(enhanced_human_data):.1f} 个关键词")
    
    return enhanced_human_data, word_frequency

def extract_keywords_from_pre_user_data(human_data, 
                                   text_field='解释',  # 指定要提取的字段
                                   sample_rate=0.02, 
                                   min_freq=2,
                                   min_words=2, 
                                   max_words=5, 
                                   min_chars=3,
                                   top_k=1000):
    """
    从human_data中提取关键词，并保存到对应索引下
    
    Args:
        human_data: 输入的人类数据列表，每个元素应该包含['message']['response']字段
        text_field: 要提取关键词的字段名，默认为'解释'
        sample_rate: 采样率
        min_freq: 最小频次
        min_words: 最小词数
        max_words: 最大词数
        min_chars: 最小字符数
        top_k: 保留的候选数量
    
    Returns:
        tuple: (enhanced_human_data, word_frequency_counter)
            - enhanced_human_data: 增强后的数据，每个元素添加了'keywords'字段
            - word_frequency_counter: 词频统计Counter对象
    """
    
    print(f"开始处理 {len(human_data)} 条数据...")
    
    # 提取文本列表
    texts = []
    valid_indices = []  # 记录有效的索引
    
    for i, item in enumerate(human_data):
        try:
            # 尝试提取指定字段的文本
            text = item['message']['user_request']
            # text = item['reponse_JSON'][text_field]
            if text and isinstance(text, str) and len(text.strip()) > 0:
                texts.append(text.strip())
                valid_indices.append(i)
        except (KeyError, TypeError) as e:
            print(f"索引 {i} 处理失败: {e}")
            continue
    print(f"成功提取 {len(texts)} 条有效文本")
    
    if not texts:
        print("没有找到有效的文本数据")
        return human_data, Counter()
    
    # 使用原有函数提取公共关键词
    word_frequency = jieba_optimized_common_substring(
        texts=texts,
        sample_rate=sample_rate,
        min_freq=min_freq,
        min_words=min_words,
        max_words=max_words,
        min_chars=min_chars,
        top_k=top_k
    )
    
    print(f"提取到 {len(word_frequency)} 个高频关键词")
    
    # 为每个数据项添加关键词
    enhanced_human_data = []
    extractor = JiebaChineseExtractor()
    
    for i, item in enumerate(human_data):
        # 复制原始数据
        enhanced_item = item.copy()
        
        # 初始化关键词列表
        keywords = []
        
        try:
            # 获取文本
            text = item['message']['user_request']
            
            if text and isinstance(text, str):
                # 提取当前文本的所有关键词
                all_segments = extractor.extract_meaningful_segments(
                    text, min_words, max_words, min_chars
                )
                
                # 只保留在高频关键词中的部分
                for segment in all_segments:
                    if segment in word_frequency:
                        keywords.append({
                            'keyword': segment,
                            'frequency': word_frequency[segment]
                        })
                
                # 按频次排序
                keywords.sort(key=lambda x: x['frequency'], reverse=True)
        
        except (KeyError, TypeError) as e:
            print(f"为索引 {i} 添加关键词时出错: {e}")
        
        # 添加关键词字段
        enhanced_item['keywords'] = keywords
        enhanced_human_data.append(enhanced_item)
    
    print(f"完成关键词提取，平均每条数据有 {sum(len(item['keywords']) for item in enhanced_human_data) / len(enhanced_human_data):.1f} 个关键词")
    
    return enhanced_human_data, word_frequency


# 测试示例
if __name__ == "__main__":
    # 测试去重功能
    sample_human_data = [
        {
            'message': {
                'response': {
                    '解释': '因为知识库未提及相关内容，所以不回复。这是一个重要的问题。'
                }
            }
        },
        {
            'message': {
                'response': {
                    '解释': '知识库未提及相关内容，因此无法回答。请提供更多信息。'
                }
            }
        },
        {
            'message': {
                'response': {
                    '解释': '未提及相关内容的问题比较复杂，需要进一步分析。'
                }
            }
        }
    ]
    
    # 调用封装的函数
    enhanced_data, word_freq = extract_keywords_from_human_data(
        human_data=sample_human_data,
        text_field='解释',
        sample_rate=1.0,  # 使用全量数据
        min_freq=2,
        min_words=2,
        max_words=5,
        min_chars=3
    )
    
    print("\n=== 词频统计结果 ===")
    for word, freq in word_freq.most_common(20):
        print(f"'{word}': {freq}次")
    
    print("\n=== 每条数据的关键词 ===")
    for i, item in enumerate(enhanced_data):
        print(f"\n数据 {i}:")
        print(f"原文: {item['message']['response']['解释']}")
        print(f"关键词: {[kw['keyword'] for kw in item['keywords']]}")
    
    # 测试去重效果
    print("\n=== 测试去重效果 ===")
    extractor = JiebaChineseExtractor()
    test_text = "因为知识库未提及相关内容，所以不回复"
    segments = extractor.extract_meaningful_segments(test_text, min_words=2, max_words=5, min_chars=3)
    print(f"原文: {test_text}")
    print(f"提取的关键词: {segments}")
    
    # 测试包含关系去重
    test_segments = ["未提及相关内容", "相关内容", "未提及", "知识库", "知识库未提及", "提及相关"]
    deduplicated = extractor._remove_substring_duplicates(test_segments)
    print(f"\n去重前: {test_segments}")
    print(f"去重后: {deduplicated}")