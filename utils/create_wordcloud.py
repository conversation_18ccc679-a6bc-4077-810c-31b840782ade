from wordcloud import WordCloud
import openpyxl
from openpyxl.drawing.image import Image
import time
import io
import os
import platform
from PIL import Image as PILImage


def get_chinese_font_path():
    """
    自动获取系统中文字体路径
    
    返回:
    str - 字体文件路径，如果找不到则返回None
    """
    system = platform.system()
    
    # 常见中文字体路径（按优先级排序）
    font_paths = []
    
    if system == "Windows":
        font_paths = [
            "C:/Windows/Fonts/simhei.ttf",      # 黑体
            "C:/Windows/Fonts/simsun.ttc",      # 宋体
            "C:/Windows/Fonts/msyh.ttc",        # 微软雅黑
            "C:/Windows/Fonts/SIMKAI.TTF",      # 楷体
        ]
    elif system == "Darwin":  # macOS
        font_paths = [
            "/System/Library/Fonts/PingFang.ttc",       # 苹方
            "/System/Library/Fonts/STHeiti Light.ttc",  # 黑体
            "/System/Library/Fonts/Hiragino Sans GB.ttc",
            "/System/Library/Fonts/Arial Unicode MS.ttf",
        ]
    elif system == "Linux":
        font_paths = [
            # 优先查找专门的中文字体
            "/home/<USER>/.fonts/SourceHanSansCN-Regular.otf"
            "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc",     # 文泉驿微米黑
            "/usr/share/fonts/truetype/wqy/wqy-zenhei.ttc",       # 文泉驿正黑
            "/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc",  # Noto中文
            "/usr/share/fonts/truetype/arphic/ukai.ttc",          # AR PL UKai CN
            "/usr/share/fonts/truetype/arphic/uming.ttc",         # AR PL UMing CN
            "/usr/share/fonts/truetype/droid/DroidSansFallbackFull.ttf",  # Droid中文
            # 备选通用字体（可能支持中文）
            "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf",
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
        ]
    
    # 检查字体文件是否存在
    for font_path in font_paths:
        if os.path.exists(font_path):
            return font_path
    
    return None


def download_chinese_font():
    """
    下载并安装中文字体（如果没有找到合适的字体）
    
    返回:
    str - 下载的字体文件路径
    """
    import urllib.request
    
    # 创建fonts目录
    font_dir = os.path.expanduser("~/.fonts")
    if not os.path.exists(font_dir):
        os.makedirs(font_dir)
    
    font_file = os.path.join(font_dir, "NotoSansCJK-Regular.ttc")
    
    # 如果字体文件已存在，直接返回
    if os.path.exists(font_file):
        return font_file
    
    try:
        print("正在下载中文字体文件...")
        # 使用Google Fonts的开源中文字体
        font_url = "https://github.com/googlefonts/noto-cjk/raw/main/Sans/OTC/NotoSansCJK-Regular.ttc"
        urllib.request.urlretrieve(font_url, font_file)
        print(f"字体下载完成: {font_file}")
        return font_file
    except Exception as e:
        print(f"字体下载失败: {e}")
        return None


def check_font_supports_chinese(font_path):
    """
    检查字体是否支持中文字符
    
    参数:
    font_path: str - 字体文件路径
    
    返回:
    bool - 是否支持中文
    """
    if not font_path or not os.path.exists(font_path):
        return False
    
    try:
        from PIL import ImageFont, ImageDraw, Image as PILImage
        
        # 创建测试图像
        img = PILImage.new('RGB', (100, 50), color='white')
        draw = ImageDraw.Draw(img)
        
        # 尝试使用字体渲染中文
        font = ImageFont.truetype(font_path, size=20)
        draw.text((10, 10), "测试", font=font, fill='black')
        
        # 如果没有异常，认为支持中文
        return True
    except Exception:
        return False


def create_chinese_wordcloud_excel(word_freq_dict, 
                                 excel_filename='chinese_wordcloud.xlsx',
                                 png_filename=None,
                                 font_path=None,
                                 wordcloud_params=None,
                                 excel_image_position='D1',
                                 excel_image_size=(400, 200),
                                 verbose=True):
    """
    生成中文词云并插入到Excel表格中
    
    参数:
    word_freq_dict: dict - 中文词汇频率字典，如 {'Python': 124, '数据分析': 62}
    excel_filename: str - Excel文件名，默认 'chinese_wordcloud.xlsx'
    png_filename: str - PNG文件名，如果为None则不保存PNG文件
    font_path: str - 中文字体文件路径，如果为None则自动检测
    wordcloud_params: dict - WordCloud自定义参数
    excel_image_position: str - 图片在Excel中的位置，默认 'D1'
    excel_image_size: tuple - 图片在Excel中的尺寸 (width, height)
    verbose: bool - 是否显示进度信息
    
    返回:
    dict - 包含执行时间和文件路径的信息
    """
    
    if verbose:
        print("开始生成中文词云Excel...")
    
    start_time = time.time()
    
    # 自动检测中文字体
    if font_path is None:
        font_path = get_chinese_font_path()
        if verbose:
            if font_path:
                print(f"自动检测到字体: {font_path}")
            else:
                print("警告: 未找到中文字体，可能显示效果不佳")
    
    # 默认WordCloud参数（针对中文优化）
    default_params = {
        'width': 400,
        'height': 200,
        'background_color': 'white',
        'max_words': 50,
        'relative_scaling': 0.5,
        'collocations': False,
        'prefer_horizontal': 0.9,  # 中文更适合水平显示
        'min_font_size': 10,
        'max_font_size': 60,
        'font_step': 2,
        'margin': 5,
        'random_state': 42,  # 固定随机种子，保证结果一致
    }
    
    # 如果有中文字体，添加字体参数
    if font_path:
        default_params['font_path'] = font_path
    
    # 合并用户自定义参数
    if wordcloud_params:
        default_params.update(wordcloud_params)
    
    # 生成词云
    if verbose:
        print("生成中文词云...")
    wordcloud_start = time.time()
    
    try:
        wordcloud = WordCloud(**default_params).generate_from_frequencies(word_freq_dict)
        wordcloud_time = time.time() - wordcloud_start
        if verbose:
            print(f"词云生成完成，耗时: {wordcloud_time:.2f}秒")
    except Exception as e:
        if verbose:
            print(f"词云生成失败: {e}")
        return None
    
    # 转换为PIL图像
    if verbose:
        print("处理图像...")
    image_start = time.time()
    
    try:
        wordcloud_array = wordcloud.to_array()
        pil_img = PILImage.fromarray(wordcloud_array)
        
        # 保存到内存字节流
        img_bytes = io.BytesIO()
        pil_img.save(img_bytes, format='PNG', optimize=True, dpi=(150, 150))
        img_bytes.seek(0)
        
        image_time = time.time() - image_start
        if verbose:
            print(f"图像处理完成，耗时: {image_time:.2f}秒")
    except Exception as e:
        if verbose:
            print(f"图像处理失败: {e}")
        return None
    
    # 创建Excel文件
    if verbose:
        print("创建Excel文件...")
    excel_start = time.time()
    
    try:
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "词云数据"
        
        # 添加表头和数据
        ws.append(['关键词', '出现次数', '频率占比'])
        
        # 计算总频率用于计算占比
        total_freq = sum(word_freq_dict.values())
        
        # 按频率排序
        sorted_words = sorted(word_freq_dict.items(), key=lambda x: x[1], reverse=True)
        
        for word, freq in sorted_words:
            percentage = (freq / total_freq) * 100
            ws.append([word, freq, f"{percentage:.1f}%"])
        
        # 调整列宽
        ws.column_dimensions['A'].width = 15
        ws.column_dimensions['B'].width = 12
        ws.column_dimensions['C'].width = 12
        
        # 设置表头样式
        from openpyxl.styles import Font, PatternFill, Alignment
        
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        header_alignment = Alignment(horizontal="center", vertical="center")
        
        for cell in ws[1]:
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment
        
        # 插入图片
        img = Image(img_bytes)
        img.width = excel_image_size[0]
        img.height = excel_image_size[1]
        ws.add_image(img, excel_image_position)
        
        # 保存Excel文件
        wb.save(excel_filename)
        
        excel_time = time.time() - excel_start
        if verbose:
            print(f"Excel文件创建完成，耗时: {excel_time:.2f}秒")
    except Exception as e:
        if verbose:
            print(f"Excel文件创建失败: {e}")
        return None
    
    # 可选：保存PNG文件
    png_time = 0
    if png_filename:
        if verbose:
            print(f"保存PNG文件: {png_filename}")
        png_start = time.time()
        try:
            pil_img.save(png_filename, optimize=True, dpi=(300, 300))
            png_time = time.time() - png_start
            if verbose:
                print(f"PNG文件保存完成，耗时: {png_time:.2f}秒")
        except Exception as e:
            if verbose:
                print(f"PNG文件保存失败: {e}")
    
    total_time = time.time() - start_time
    if verbose:
        print(f"总耗时: {total_time:.2f}秒")
        print(f"Excel文件已保存: {excel_filename}")
        if png_filename:
            print(f"PNG文件已保存: {png_filename}")
    
    # 返回执行信息
    return {
        'excel_file': excel_filename,
        'png_file': png_filename,
        'font_path': font_path,
        'total_time': total_time,
        'wordcloud_time': wordcloud_time,
        'image_time': image_time,
        'excel_time': excel_time,
        'png_time': png_time,
        'word_count': len(word_freq_dict),
        'total_frequency': total_freq
    }


def create_chinese_wordcloud_excel_fast(word_freq_dict, filename='chinese_wordcloud_fast.xlsx', font_path=None):
    """
    快速版本：最小参数，最快速度（中文优化）
    
    参数:
    word_freq_dict: dict - 中文词汇频率字典
    filename: str - 输出文件名
    font_path: str - 字体路径
    
    返回:
    float - 总执行时间
    """
    result = create_chinese_wordcloud_excel(
        word_freq_dict=word_freq_dict,
        excel_filename=filename,
        png_filename=None,
        font_path=font_path,
        wordcloud_params={'max_words': 20, 'width': 300, 'height': 150},
        verbose=False
    )
    return result['total_time'] if result else 0


def create_chinese_wordcloud_excel_quality(word_freq_dict, filename='chinese_wordcloud_quality.xlsx', font_path=None):
    """
    高质量版本：更好的视觉效果（中文优化）
    
    参数:
    word_freq_dict: dict - 中文词汇频率字典
    filename: str - 输出文件名
    font_path: str - 字体路径
    
    返回:
    dict - 执行信息
    """
    quality_params = {
        'width': 800,
        'height': 500,
        'max_words': 100,
        'relative_scaling': 0.6,
        'prefer_horizontal': 0.8,
        'min_font_size': 12,
        'max_font_size': 80
    }
    
    return create_chinese_wordcloud_excel(
        word_freq_dict=word_freq_dict,
        excel_filename=filename,
        png_filename=filename.replace('.xlsx', '.png'),
        font_path=font_path,
        wordcloud_params=quality_params,
        excel_image_size=(600, 375)
    )


# 使用示例
if __name__ == "__main__":
    # 中文示例数据
    chinese_sample_data = {
        'Python': 124, 
        '数据分析': 89,
        '机器学习': 67,
        '人工智能': 56,
        '深度学习': 45,
        '自然语言处理': 34,
        '计算机视觉': 28,
        '数据挖掘': 23,
        '算法': 78,
        '编程': 92,
        '开发': 65,
        '技术': 54
    }
    
    print("=== 中文词云生成测试 ===")
    
    # 检测系统字体
    font_path = get_chinese_font_path()
    if font_path:
        print(f"检测到中文字体: {font_path}")
    else:
        print("未检测到中文字体，建议手动指定字体路径")
    
    print("\n=== 快速版本 ===")
    fast_time = create_chinese_wordcloud_excel_fast(chinese_sample_data, font_path=font_path)
    print(f"快速版本完成，耗时: {fast_time:.2f}秒\n")
    
    print("=== 标准版本 ===")
    result = create_chinese_wordcloud_excel(
        word_freq_dict=chinese_sample_data,
        excel_filename='my_chinese_wordcloud.xlsx',
        png_filename='my_chinese_wordcloud.png',
        font_path=font_path
    )
    if result:
        print(f"标准版本详细信息:")
        for key, value in result.items():
            print(f"  {key}: {value}")
    
    print("\n=== 高质量版本 ===")
    quality_result = create_chinese_wordcloud_excel_quality(
        chinese_sample_data, 
        font_path=font_path
    )
    if quality_result:
        print(f"高质量版本完成，总耗时: {quality_result['total_time']:.2f}秒")
        print(f"包含词汇数量: {quality_result['word_count']}")
        print(f"总频率: {quality_result['total_frequency']}")