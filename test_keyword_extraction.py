#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
关键词提取算法测试脚本
"""

import time
from utils.jieba_optimized_common_substring import (
    AdvancedChineseKeywordExtractor,
    extract_keywords_with_advanced_algorithm
)

def test_single_text_extraction():
    """测试单个文本的关键词提取"""
    print("=== 单文本关键词提取测试 ===")
    
    extractor = AdvancedChineseKeywordExtractor()
    
    test_texts = [
        "用户反馈商品页面加载速度慢，影响购买体验，需要优化系统性能。",
        "客服系统无法正常处理用户咨询，导致客户满意度下降，急需修复。",
        "订单管理功能存在bug，用户无法查看历史订单，影响业务流程。",
        "支付接口频繁超时，用户支付失败率较高，需要技术团队紧急处理。",
        "商品推荐算法效果不佳，用户点击率和转化率都比较低。"
    ]
    
    for i, text in enumerate(test_texts):
        print(f"\n文本 {i+1}: {text}")
        
        start_time = time.time()
        keywords = extractor.extract_keywords(text, top_k=8, min_chars=2)
        end_time = time.time()
        
        print(f"处理时间: {(end_time - start_time)*1000:.2f}ms")
        print("关键词:")
        for kw in keywords:
            print(f"  {kw['keyword']} (分数: {kw['score']:.3f}, 类型: {kw['type']})")

def test_batch_extraction():
    """测试批量文本关键词提取"""
    print("\n\n=== 批量文本关键词提取测试 ===")
    
    # 模拟更多的测试数据
    test_texts = [
        "用户反馈商品页面加载速度慢，影响购买体验，需要优化系统性能。",
        "客服系统无法正常处理用户咨询，导致客户满意度下降，急需修复。",
        "订单管理功能存在bug，用户无法查看历史订单，影响业务流程。",
        "支付接口频繁超时，用户支付失败率较高，需要技术团队紧急处理。",
        "商品推荐算法效果不佳，用户点击率和转化率都比较低。",
        "数据库查询性能问题导致系统响应缓慢，用户体验差。",
        "移动端应用经常崩溃，用户投诉较多，需要稳定性优化。",
        "搜索功能准确性不高，用户很难找到想要的商品。",
        "用户注册流程复杂，注册成功率低，影响新用户获取。",
        "商品库存管理混乱，经常出现超卖或缺货情况。",
        "物流信息更新不及时，用户无法准确跟踪订单状态。",
        "优惠券系统存在漏洞，被恶意刷取，造成经济损失。",
        "用户评价系统功能单一，无法满足多样化需求。",
        "商品分类不够清晰，用户浏览体验不佳。",
        "客户服务响应时间长，用户等待时间过久。"
    ] * 10  # 扩展到150个文本
    
    print(f"测试数据量: {len(test_texts)} 个文本")
    
    start_time = time.time()
    word_frequency = extract_keywords_with_advanced_algorithm(
        texts=test_texts,
        sample_rate=0.5,  # 使用50%的数据进行采样
        min_freq=3,
        top_k=100,
        min_chars=2
    )
    end_time = time.time()
    
    print(f"\n总处理时间: {end_time - start_time:.2f}秒")
    print(f"提取到 {len(word_frequency)} 个高频关键词")
    
    print("\n前20个高频关键词:")
    for word, freq in word_frequency.most_common(20):
        print(f"  '{word}': {freq}次")

def test_keyword_quality():
    """测试关键词质量"""
    print("\n\n=== 关键词质量测试 ===")
    
    extractor = AdvancedChineseKeywordExtractor()
    
    # 测试不同类型的文本
    test_cases = [
        {
            "name": "技术问题描述",
            "text": "系统在高并发情况下出现数据库连接池耗尽的问题，导致用户请求超时，需要优化数据库连接管理和增加缓存机制。"
        },
        {
            "name": "用户反馈",
            "text": "商品详情页的图片加载很慢，而且有时候会显示不出来，希望能够优化一下，提升用户体验。"
        },
        {
            "name": "业务需求",
            "text": "需要开发一个新的会员积分系统，支持多种积分获取方式和兑换功能，提高用户粘性和活跃度。"
        },
        {
            "name": "错误报告",
            "text": "用户在支付过程中遇到网络异常，支付失败但订单状态未正确更新，导致重复扣款问题。"
        }
    ]
    
    for case in test_cases:
        print(f"\n{case['name']}:")
        print(f"原文: {case['text']}")
        
        keywords = extractor.extract_keywords(case['text'], top_k=10, min_chars=2)
        
        print("关键词:")
        for kw in keywords:
            print(f"  {kw['keyword']} (分数: {kw['score']:.3f})")

if __name__ == "__main__":
    # 运行所有测试
    test_single_text_extraction()
    test_batch_extraction()
    test_keyword_quality()
    
    print("\n=== 测试完成 ===")
