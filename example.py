from utils.load_json import load_jsonl
from utils.spilit_log_message import split_log_pre_message
from utils.jieba_optimized_common_substring import extract_keywords_from_pre_user_data
from utils.create_wordcloud import create_chinese_wordcloud_excel_quality
if __name__ == '__main__':
    log_path = './data/807凌晨改写log.json'
    out_path = './output'
    # 读取日志
    log_data = load_jsonl(log_path)
    # 对日志进行切分处理 
    # checked_data[i]["user_request"]是用户询问的问题
    checked_data, error_data = split_log_pre_message(log_data)
    #
    enhanced_human_data, word_frequency = extract_keywords_from_pre_user_data(checked_data,max_words=15)
    #创建词云
    result = create_chinese_wordcloud_excel_quality(
        word_frequency, 
        filename=out_path+'/chinese_wordcloud_quality.xlsx', 
    )
    print(checked_data)